# Environment variables for Worker Service Cloud Run deployment - DEVELOPMENT
# This file is used with --env-vars-file flag in gcloud run deploy

ASPNETCORE_ENVIRONMENT: Development
PORT: "8080"

# Firestore Configuration
Firestore__ProjectId: tranquil-bison-465923-v9
Firestore__DatabaseId: gallerytuner
Firestore__UseEmulator: "false"
Firestore__EmulatorHost: ""

# Google Cloud Configuration
GoogleCloud__ProjectId: tranquil-bison-465923-v9
GoogleCloud__Region: us-central1
GoogleCloud__InputBucketName: tranquil-bison-465923-v9-vidcompressor-input-dev
GoogleCloud__OutputBucketName: tranquil-bison-465923-v9-vidcompressor-output-dev
GoogleCloud__TempBucketName: tranquil-bison-465923-v9-vidcompressor-temp-dev
GoogleCloud__ServiceAccountKeyPath: /secrets/transcoder-service-account-key

# Cloud Tasks Configuration
GoogleCloud__CloudTasks__ProjectId: tranquil-bison-465923-v9
GoogleCloud__CloudTasks__Location: us-central1
GoogleCloud__CloudTasks__QueueName: video-compression-jobs-dev
GoogleCloud__CloudTasks__HandlerUrl: https://vidcompressor-worker-service-dev-tranquil-bison-465923-v9.a.run.app

# Transcoder Configuration
GoogleCloud__Transcoder__Location: us-central1

# SignalR Configuration
SignalR__ApiServerUrl: https://vidcompressor-api-server-dev-tranquil-bison-465923-v9.a.run.app
